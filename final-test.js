/**
 * Final verification test
 */

const { obfuscateCode } = require('./src/obfuscator');

async function finalTest() {
    console.log('🔍 Final Verification Test...\n');
    
    const testCode = `
        function sayHello(name) {
            const message = "Hello, " + name + "!";
            console.log(message);
            alert("Welcome to the application!");
            return message;
        }
        
        sayHello("World");
    `;
    
    try {
        console.log('📋 Original Code:');
        console.log(testCode);
        console.log('\n' + '='.repeat(60) + '\n');
        
        const obfuscatedCode = await obfuscateCode(testCode, 'browser');
        
        console.log('✅ Obfuscation completed successfully!');
        console.log('\n📄 Obfuscated Code:');
        console.log(obfuscatedCode);
        
        // Verify key features
        const checks = [
            {
                name: 'Custom Base64 decoder present',
                test: () => obfuscatedCode.includes('base64Chars'),
                expected: true
            },
            {
                name: 'No atob dependency',
                test: () => !obfuscatedCode.includes('atob'),
                expected: true
            },
            {
                name: 'No Buffer dependency',
                test: () => !obfuscatedCode.includes('Buffer.from'),
                expected: true
            },
            {
                name: 'No junk character array exposed',
                test: () => !obfuscatedCode.includes('["@","#"') && !obfuscatedCode.includes("['@','#'"),
                expected: true
            },
            {
                name: 'Contains obfuscated strings',
                test: () => /var\s+\w+\s*=\s*\[/.test(obfuscatedCode),
                expected: true
            },
            {
                name: 'Original strings concealed',
                test: () => !obfuscatedCode.includes('"Hello, "') && !obfuscatedCode.includes('"Welcome to the application!"'),
                expected: true
            }
        ];
        
        console.log('\n🔍 Verification Checks:');
        let allPassed = true;
        
        checks.forEach(check => {
            const result = check.test();
            const status = result === check.expected ? '✅' : '❌';
            console.log(`${status} ${check.name}: ${result === check.expected ? 'PASS' : 'FAIL'}`);
            if (result !== check.expected) {
                allPassed = false;
            }
        });
        
        console.log('\n' + '='.repeat(60));
        if (allPassed) {
            console.log('🎉 All verification checks passed!');
            console.log('✅ String concealing with custom decoder is working perfectly');
        } else {
            console.log('❌ Some verification checks failed');
        }
        
        return allPassed;
        
    } catch (error) {
        console.error('❌ Final test failed:', error.message);
        return false;
    }
}

if (require.main === module) {
    finalTest().then(success => {
        if (!success) {
            process.exit(1);
        }
    }).catch(console.error);
}

module.exports = { finalTest };
