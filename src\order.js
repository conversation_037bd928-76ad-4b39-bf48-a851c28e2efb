/**
 * Obfuscation Process Order Configuration
 * 
 * This file defines the order and configuration for different obfuscation processes.
 * The order is crucial as some processes depend on the output of others.
 */

const { processGlobalConcealing } = require('./globalConcealing');
const { processSimplify } = require('./simplify');
const { processJsConfuser } = require('./jsconfuser');
const { processPropertiesToString } = require('./propertiestostring');
const { processStringConcealing } = require('./stringConcealing');

/**
 * Obfuscation process definitions with their order and configuration
 */
const OBFUSCATION_PROCESSES = [
    {
        id: 'simplify',
        name: 'Dot Notation Simplification',
        description: 'Convert dot notation to bracket notation for global access',
        order: 1,
        processor: processSimplify,
        enabled: true,
        reason: 'Prepare for global concealing by converting dot notation to bracket notation'
    },
    {
        id: 'properties_to_string',
        name: 'Properties to String',
        description: 'Convert object properties from identifier and numeric notation to string notation',
        order: 2,
        processor: processPropertiesToString,
        enabled: true,
        reason: 'Prepare for global concealing by converting object properties to string notation'
    },
    {
        id: 'global_concealing',
        name: 'Global Concealing',
        description: 'Hide global variable access patterns',
        order: 3,
        processor: processGlobalConcealing,
        enabled: true,
        reason: 'Must run after simplify to work with bracket notation'
    },
    {
        id: 'js_confuser',
        name: 'JS-Confuser Primary Obfuscation',
        description: 'Primary obfuscation using js-confuser library with comprehensive transformations',
        order: 5,
        processor: processJsConfuser,
        enabled: true,
        reason: 'Run first to apply comprehensive obfuscation before custom transformations'
    },
    {
        id: 'string_concealing',
        name: 'String Concealing',
        description: 'Obfuscate string literals using Base64 encoding with custom obfuscation',
        order: 4,
        processor: processStringConcealing,
        enabled: true,
        reason: 'Run after JS-Confuser to hide remaining string literals with Base64 and junk characters'
    }
];

/**
 * Get processes in execution order
 * @param {boolean} enabledOnly - Return only enabled processes
 * @returns {Array} Array of processes sorted by order
 */
function getProcessesInOrder(enabledOnly = true) {
    let processes = [...OBFUSCATION_PROCESSES];
    
    if (enabledOnly) {
        processes = processes.filter(process => process.enabled);
    }
    
    return processes.sort((a, b) => a.order - b.order);
}

/**
 * Get a specific process by ID
 * @param {string} processId - The process ID
 * @returns {Object|null} The process object or null if not found
 */
function getProcessById(processId) {
    return OBFUSCATION_PROCESSES.find(process => process.id === processId) || null;
}

/**
 * Enable or disable a process
 * @param {string} processId - The process ID
 * @param {boolean} enabled - Whether to enable or disable
 * @returns {boolean} Success status
 */
function setProcessEnabled(processId, enabled) {
    const process = getProcessById(processId);
    if (process) {
        process.enabled = enabled;
        return true;
    }
    return false;
}

/**
 * Change the order of a process
 * @param {string} processId - The process ID
 * @param {number} newOrder - The new order number
 * @returns {boolean} Success status
 */
function setProcessOrder(processId, newOrder) {
    const process = getProcessById(processId);
    if (process && newOrder > 0) {
        process.order = newOrder;
        return true;
    }
    return false;
}

/**
 * Validate process order configuration
 * @returns {Object} Validation result with any issues found
 */
function validateProcessOrder() {
    const processes = getProcessesInOrder(false);
    const issues = [];
    
    // Check for duplicate orders
    const orders = processes.map(p => p.order);
    const duplicateOrders = orders.filter((order, index) => orders.indexOf(order) !== index);
    if (duplicateOrders.length > 0) {
        issues.push(`Duplicate orders found: ${duplicateOrders.join(', ')}`);
    }
    
    // Check for missing orders in sequence
    const enabledProcesses = processes.filter(p => p.enabled);
    const enabledOrders = enabledProcesses.map(p => p.order).sort((a, b) => a - b);
    for (let i = 1; i < enabledOrders.length; i++) {
        if (enabledOrders[i] - enabledOrders[i-1] > 1) {
            issues.push(`Gap in order sequence between ${enabledOrders[i-1]} and ${enabledOrders[i]}`);
        }
    }
    
    return {
        valid: issues.length === 0,
        issues: issues
    };
}

/**
 * Execute processes in order
 * @param {string} inputCode - The code to process
 * @param {Function} trackingCallback - Optional callback for tracking events
 * @returns {Object} Result with processed code and execution details
 */
async function executeProcessesInOrder(inputCode, trackingCallback = null) {
    const processes = getProcessesInOrder(true);
    let currentCode = inputCode;
    const executionDetails = [];
    
    for (const process of processes) {
        try {
            if (trackingCallback) {
                trackingCallback(`${process.id}_start`, {
                    processName: process.name,
                    codeSize: Buffer.byteLength(currentCode, 'utf8')
                });
            }
            
            const startTime = Date.now();
            const result = await process.processor(currentCode);
            const duration = Date.now() - startTime;
            
            if (result && result.code) {
                currentCode = result.code;
                
                const executionDetail = {
                    processId: process.id,
                    processName: process.name,
                    duration: duration,
                    success: result.success !== false,
                    ...result
                };
                
                executionDetails.push(executionDetail);
                
                if (trackingCallback) {
                    trackingCallback(`${process.id}_success`, executionDetail);
                }
            } else {
                throw new Error(`Process ${process.id} returned invalid result`);
            }
            
        } catch (error) {
            const errorDetail = {
                processId: process.id,
                processName: process.name,
                error: error.message,
                success: false
            };
            
            executionDetails.push(errorDetail);
            
            if (trackingCallback) {
                trackingCallback(`${process.id}_error`, errorDetail);
            }
            
            throw new Error(`Process ${process.id} failed: ${error.message}`);
        }
    }
    
    return {
        code: currentCode,
        executionDetails: executionDetails,
        totalProcesses: processes.length,
        successfulProcesses: executionDetails.filter(d => d.success).length
    };
}

module.exports = {
    OBFUSCATION_PROCESSES,
    getProcessesInOrder,
    getProcessById,
    setProcessEnabled,
    setProcessOrder,
    validateProcessOrder,
    executeProcessesInOrder
};
