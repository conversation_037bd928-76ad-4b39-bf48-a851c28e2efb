/**
 * Test to see if "hello" string gets concealed
 */

const { obfuscateCode } = require('./src/obfuscator');

async function testHelloConcealing() {
    console.log('🔍 Testing "hello" string concealing...\n');
    
    const testCode = `alert("hello");`;
    
    try {
        console.log('📋 Original Code:');
        console.log(testCode);
        console.log('\n' + '='.repeat(60) + '\n');
        
        const obfuscatedCode = await obfuscateCode(testCode, 'browser');
        
        console.log('📄 Obfuscated Code:');
        console.log(obfuscatedCode);
        
        // Check if "hello" is still visible
        const helloVisible = obfuscatedCode.includes('"hello"') || obfuscatedCode.includes("'hello'");
        
        console.log('\n🔍 Analysis:');
        console.log(`❌ "hello" still visible: ${helloVisible ? 'YES' : 'NO'}`);
        
        if (helloVisible) {
            console.log('⚠️  The string "hello" was not properly concealed!');
            
            // Look for string arrays that might contain encoded versions
            const stringArrayMatches = obfuscatedCode.match(/\[\s*"[^"]*"\s*(?:,\s*"[^"]*"\s*)*\]/g);
            if (stringArrayMatches) {
                console.log('\n📊 Found string arrays:');
                stringArrayMatches.forEach((match, index) => {
                    console.log(`  ${index + 1}. ${match}`);
                });
            }
        } else {
            console.log('✅ String "hello" successfully concealed!');
        }
        
        return !helloVisible;
        
    } catch (error) {
        console.error('❌ Test failed:', error.message);
        return false;
    }
}

if (require.main === module) {
    testHelloConcealing().then(success => {
        if (success) {
            console.log('\n🎉 Test passed! String concealing is working.');
        } else {
            console.log('\n❌ Test failed! String concealing needs improvement.');
        }
    }).catch(console.error);
}

module.exports = { testHelloConcealing };
