/**
 * String Concealing Module
 * 
 * This module provides advanced string concealing functionality using Base64 encoding
 * with custom obfuscation techniques including padding removal and junk character insertion.
 */

const parser = require('@babel/parser');
const traverse = require('@babel/traverse').default;
const generate = require('@babel/generator').default;
const t = require('@babel/types');
const { generateRandomName } = require('./utils');

/**
 * Characters used for junk insertion to make Base64 strings look more obfuscated
 * Carefully selected to avoid conflicts with Base64 character set and JavaScript string escaping
 */
const JUNK_CHARS = ['@', '#', '%', '$', '!', '*', '&', '^', '~', '|', '?', '<', '>', ':', ';', '{', '}', '[', ']', '(', ')'];

/**
 * Minimum string length to consider for concealing
 */
const MIN_STRING_LENGTH = 3;

/**
 * Encodes a string using Base64 with custom obfuscation
 * Removes padding and inserts junk characters as per user preference
 * @param {string} str - String to encode
 * @returns {string} - Obfuscated Base64 encoded string
 */
function encodeStringWithObfuscation(str) {
    // First, encode with Base64
    let base64Encoded = Buffer.from(str, 'utf8').toString('base64');
    
    // Remove padding (==) as per user preference
    base64Encoded = base64Encoded.replace(/=+$/, '');
    
    // Insert junk characters to make it look more obfuscated
    let obfuscated = '';
    for (let i = 0; i < base64Encoded.length; i++) {
        obfuscated += base64Encoded[i];
        
        // Randomly insert junk characters (about 30% chance per character)
        if (Math.random() < 0.3) {
            const junkChar = JUNK_CHARS[Math.floor(Math.random() * JUNK_CHARS.length)];
            obfuscated += junkChar;
        }
    }
    
    return obfuscated;
}

/**
 * Generates the decoder function code that can reverse the obfuscation
 * @param {string} decoderFunctionName - Name of the decoder function
 * @returns {string} - JavaScript code for the decoder function
 */
function generateDecoderFunction(decoderFunctionName) {
    return `
function ${decoderFunctionName}(obfuscatedStr) {
    // Custom Base64 character set
    var base64Chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';
    var cleanStr = '';

    // Remove junk characters by keeping only valid Base64 characters
    for (var i = 0; i < obfuscatedStr.length; i++) {
        var char = obfuscatedStr[i];
        if (base64Chars.indexOf(char) !== -1) {
            cleanStr += char;
        }
    }

    // Add padding back if needed
    while (cleanStr.length % 4 !== 0) {
        cleanStr += '=';
    }

    // Custom Base64 decoder
    var result = '';
    var buffer = 0;
    var bitsCollected = 0;

    for (var i = 0; i < cleanStr.length; i++) {
        var char = cleanStr[i];
        if (char === '=') break;

        var charIndex = base64Chars.indexOf(char);
        if (charIndex === -1) continue;

        buffer = (buffer << 6) | charIndex;
        bitsCollected += 6;

        if (bitsCollected >= 8) {
            bitsCollected -= 8;
            var byte = (buffer >> bitsCollected) & 0xFF;
            result += String.fromCharCode(byte);
        }
    }

    // Handle UTF-8 decoding
    try {
        return decodeURIComponent(escape(result));
    } catch (e) {
        return result;
    }
}`;
}

/**
 * Detects string literals in the code that should be concealed
 * @param {string} code - JavaScript code to analyze
 * @returns {Set} - Set of string values found in the code
 */
function detectStringLiterals(code) {
    const foundStrings = new Set();
    
    try {
        const ast = parser.parse(code, {
            sourceType: 'module',
            allowImportExportEverywhere: true,
            allowReturnOutsideFunction: true,
            plugins: [
                'jsx',
                'typescript',
                'decorators-legacy',
                'classProperties',
                'objectRestSpread',
                'functionBind',
                'exportDefaultFrom',
                'exportNamespaceFrom',
                'dynamicImport',
                'nullishCoalescingOperator',
                'optionalChaining'
            ]
        });

        traverse(ast, {
            StringLiteral(path) {
                const value = path.node.value;
                
                // Skip very short strings
                if (value.length < MIN_STRING_LENGTH) {
                    return;
                }
                
                // Skip import/require statements
                if (isModuleImport(path)) {
                    return;
                }
                
                // Skip strings that look like they might be important identifiers
                if (isImportantIdentifier(value)) {
                    return;
                }
                
                foundStrings.add(value);
            }
        });
    } catch (error) {
        console.error('Error detecting string literals:', error);
    }
    
    return foundStrings;
}

/**
 * Checks if a path represents a module import
 * @param {Object} path - Babel path object
 * @returns {boolean} - True if this is a module import
 */
function isModuleImport(path) {
    // Check if this string is an argument to require() call
    const parent = path.parent;
    if (parent && parent.type === 'CallExpression' &&
        parent.callee && parent.callee.name === 'require' &&
        parent.arguments[0] === path.node) {
        return true;
    }

    // Check if parent is import statement
    if (path.findParent(p => p.isImportDeclaration() || p.isExportDeclaration())) {
        return true;
    }

    return false;
}

/**
 * Checks if a string value looks like an important identifier that shouldn't be obfuscated
 * @param {string} value - String value to check
 * @returns {boolean} - True if this looks like an important identifier
 */
function isImportantIdentifier(value) {
    // Skip common important strings
    const importantPatterns = [
        /^[a-zA-Z_$][a-zA-Z0-9_$]*$/, // Simple identifiers
        /^https?:\/\//, // URLs
        /^\/[^\/]/, // File paths starting with /
        /^\.[\/\\]/, // Relative paths
        /^[A-Z_]+$/, // Constants
        /^(get|set|has|delete|clear|keys|values|entries)$/, // Common method names
    ];
    
    return importantPatterns.some(pattern => pattern.test(value));
}

/**
 * Transforms JavaScript code by replacing string literals with obfuscated versions
 * @param {string} code - JavaScript code to transform
 * @param {Map} stringMapping - Map of original strings to their indices
 * @param {string} accessorFunctionName - Name of the string accessor function
 * @returns {string} - Transformed code
 */
function transformStringLiterals(code, stringMapping, accessorFunctionName) {
    try {
        const ast = parser.parse(code, {
            sourceType: 'module',
            allowImportExportEverywhere: true,
            allowReturnOutsideFunction: true,
            plugins: [
                'jsx',
                'typescript',
                'decorators-legacy',
                'classProperties',
                'objectRestSpread',
                'functionBind',
                'exportDefaultFrom',
                'exportNamespaceFrom',
                'dynamicImport',
                'nullishCoalescingOperator',
                'optionalChaining'
            ]
        });

        let transformCount = 0;

        traverse(ast, {
            StringLiteral(path) {
                const value = path.node.value;

                // Skip very short strings
                if (value.length < MIN_STRING_LENGTH) {
                    return;
                }

                // Skip import/require statements
                if (isModuleImport(path)) {
                    return;
                }

                // Skip strings that look like they might be important identifiers
                if (isImportantIdentifier(value)) {
                    return;
                }

                // Check if we have this string in our mapping
                if (stringMapping.has(value)) {
                    const index = stringMapping.get(value);

                    // Replace with accessor call
                    path.replaceWith(
                        t.callExpression(
                            t.identifier(accessorFunctionName),
                            [t.numericLiteral(index)]
                        )
                    );

                    transformCount++;
                }
            }
        });

        return {
            code: generate(ast).code,
            transformCount
        };
    } catch (error) {
        console.error('Error transforming string literals:', error);
        return {
            code,
            transformCount: 0
        };
    }
}

/**
 * Generates the string array and accessor function runtime
 * @param {Map} stringMapping - Map of original strings to their indices
 * @param {string} accessorFunctionName - Name of the accessor function
 * @param {string} decoderFunctionName - Name of the decoder function
 * @returns {Object} - Object containing the runtime code and function names
 */
function generateStringAccessorRuntime(stringMapping, accessorFunctionName, decoderFunctionName) {
    // Create array of obfuscated strings
    const obfuscatedStrings = [];
    const sortedEntries = Array.from(stringMapping.entries()).sort((a, b) => a[1] - b[1]);

    for (const [originalString, index] of sortedEntries) {
        obfuscatedStrings[index] = encodeStringWithObfuscation(originalString);
    }

    // Generate the string array
    const stringArrayName = generateRandomName();
    const cacheArrayName = generateRandomName();

    const stringArrayCode = `var ${stringArrayName} = ${JSON.stringify(obfuscatedStrings)};`;
    const cacheArrayCode = `var ${cacheArrayName} = {};`;

    // Generate the decoder function
    const decoderCode = generateDecoderFunction(decoderFunctionName);

    // Generate the accessor function
    const accessorCode = `
function ${accessorFunctionName}(index) {
    if (typeof ${cacheArrayName}[index] === 'undefined') {
        ${cacheArrayName}[index] = ${decoderFunctionName}(${stringArrayName}[index]);
    }
    return ${cacheArrayName}[index];
}`;

    const runtimeCode = [
        stringArrayCode,
        cacheArrayCode,
        decoderCode,
        accessorCode
    ].join('\n\n');

    return {
        code: runtimeCode,
        stringArrayName,
        cacheArrayName,
        accessorFunctionName,
        decoderFunctionName
    };
}

/**
 * Main function to process JavaScript code with string concealing
 * @param {string} code - JavaScript code to process
 * @returns {Object} - Object containing transformed code and metadata
 */
function processStringConcealing(code) {
    try {
        // Step 1: Detect string literals in the code
        const foundStrings = detectStringLiterals(code);

        if (foundStrings.size === 0) {
            return {
                code,
                stringAccessorRuntime: '',
                concealedStringsCount: 0,
                foundStrings: [],
                success: true
            };
        }

        // Step 2: Create mapping for found strings
        const stringMapping = new Map();
        let index = 0;
        foundStrings.forEach(stringValue => {
            stringMapping.set(stringValue, index++);
        });

        // Step 3: Generate function names
        const accessorFunctionName = generateRandomName();
        const decoderFunctionName = generateRandomName();

        // Step 4: Generate string accessor runtime
        const runtimeResult = generateStringAccessorRuntime(
            stringMapping,
            accessorFunctionName,
            decoderFunctionName
        );
        const stringAccessorRuntime = runtimeResult.code;

        // Step 5: Transform the code
        const transformResult = transformStringLiterals(code, stringMapping, accessorFunctionName);
        const transformedCode = transformResult.code;

        // Step 6: Combine runtime with transformed code
        const finalCode = stringAccessorRuntime + '\n\n' + transformedCode;

        return {
            code: finalCode,
            stringAccessorRuntime,
            stringMapping,
            concealedStringsCount: foundStrings.size,
            foundStrings: Array.from(foundStrings),
            transformCount: transformResult.transformCount,
            accessorFunctionName,
            decoderFunctionName,
            success: true
        };
    } catch (error) {
        console.error('Error processing string concealing:', error);
        return {
            code,
            stringAccessorRuntime: '',
            concealedStringsCount: 0,
            foundStrings: [],
            transformCount: 0,
            success: false,
            error: error.message
        };
    }
}

module.exports = {
    processStringConcealing,
    encodeStringWithObfuscation,
    generateDecoderFunction,
    detectStringLiterals
};
