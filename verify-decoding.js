/**
 * Verification script to test that our Base64 obfuscation can be properly decoded
 */

const { encodeStringWithObfuscation, generateDecoderFunction } = require('./src/stringConcealing');

function testEncodingDecoding() {
    console.log('🔍 Testing Base64 Encoding/Decoding with Obfuscation...\n');
    
    const testStrings = [
        "Hello World!",
        "This is a test string",
        "JavaScript obfuscation",
        "Special chars: @#$%^&*()",
        "Unicode: 🚀 🎉 ✅",
        "Long string with multiple words and punctuation marks!"
    ];
    
    // Generate a decoder function
    const decoderFunctionName = 'testDecoder';
    const decoderCode = generateDecoderFunction(decoderFunctionName);
    
    console.log('📝 Generated Decoder Function:');
    console.log(decoderCode);
    console.log('\n' + '='.repeat(60) + '\n');
    
    // Create the decoder function in the current context
    eval(decoderCode);
    
    console.log('🧪 Testing Encoding/Decoding for each string:\n');
    
    let allTestsPassed = true;
    
    testStrings.forEach((originalString, index) => {
        console.log(`Test ${index + 1}: "${originalString}"`);
        
        // Encode the string
        const encoded = encodeStringWithObfuscation(originalString);
        console.log(`  📤 Encoded: "${encoded}"`);
        
        // Decode the string using our decoder
        const decoded = eval(`${decoderFunctionName}("${encoded}")`);
        console.log(`  📥 Decoded: "${decoded}"`);
        
        // Verify they match
        const matches = decoded === originalString;
        console.log(`  ${matches ? '✅' : '❌'} Match: ${matches}`);
        
        if (!matches) {
            allTestsPassed = false;
            console.log(`  ⚠️  Expected: "${originalString}"`);
            console.log(`  ⚠️  Got: "${decoded}"`);
        }
        
        console.log('');
    });
    
    console.log('='.repeat(60));
    console.log(`🎯 Overall Result: ${allTestsPassed ? '✅ All tests passed!' : '❌ Some tests failed!'}`);
    
    return allTestsPassed;
}

function demonstrateObfuscation() {
    console.log('\n🎨 Demonstrating Obfuscation Features:\n');
    
    const testString = "Hello World!";
    console.log(`Original string: "${testString}"`);
    
    // Show multiple encodings of the same string to demonstrate randomness
    console.log('\nMultiple encodings of the same string (showing randomness):');
    for (let i = 1; i <= 5; i++) {
        const encoded = encodeStringWithObfuscation(testString);
        console.log(`  ${i}. "${encoded}"`);
    }
    
    // Show the Base64 without obfuscation for comparison
    const plainBase64 = Buffer.from(testString, 'utf8').toString('base64');
    console.log(`\nPlain Base64 (for comparison): "${plainBase64}"`);
    
    // Show how padding is removed
    const base64WithPadding = Buffer.from("Hi", 'utf8').toString('base64');
    const obfuscatedWithoutPadding = encodeStringWithObfuscation("Hi");
    console.log(`\nPadding removal demonstration:`);
    console.log(`  Original "Hi" -> Base64 with padding: "${base64WithPadding}"`);
    console.log(`  Our obfuscated version: "${obfuscatedWithoutPadding}"`);
}

// Run the tests
if (require.main === module) {
    const success = testEncodingDecoding();
    demonstrateObfuscation();
    
    if (success) {
        console.log('\n🎉 All encoding/decoding tests passed successfully!');
        console.log('✅ String concealing implementation is working correctly.');
    } else {
        console.log('\n❌ Some tests failed. Please check the implementation.');
        process.exit(1);
    }
}

module.exports = {
    testEncodingDecoding,
    demonstrateObfuscation
};
